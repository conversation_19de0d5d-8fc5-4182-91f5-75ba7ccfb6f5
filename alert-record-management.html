<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警记录管理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 日期选择器 -->
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <style>
        .alert-status-0 { color: #dc3545; font-weight: bold; }
        .alert-status-1 { color: #28a745; }
        .dispose-status-0 { color: #ffc107; font-weight: bold; }
        .dispose-status-1 { color: #28a745; }
        .table-responsive { max-height: 600px; overflow-y: auto; }
        .btn-group-sm .btn { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
        .search-form { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status-open { background-color: #fff3cd; color: #856404; }
        .status-closed { background-color: #d1ecf1; color: #0c5460; }
        .status-undisposed { background-color: #f8d7da; color: #721c24; }
        .status-disposed { background-color: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-exclamation-triangle text-warning"></i> 预警记录管理</h2>

                <!-- 查询条件 -->
                <div class="search-form">
                    <form id="searchForm">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">预警事项编码</label>
                                <input type="text" class="form-control" id="itemCode" placeholder="请输入事项编码">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警对象ID</label>
                                <input type="text" class="form-control" id="alertObjectId" placeholder="请输入对象ID">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警状态</label>
                                <select class="form-select" id="alertStatus">
                                    <option value="">全部</option>
                                    <option value="0">未关闭</option>
                                    <option value="1">已关闭</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">处置状态</label>
                                <select class="form-select" id="disposeStatus">
                                    <option value="">全部</option>
                                    <option value="0">未处置</option>
                                    <option value="1">已处置</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警时间(开始)</label>
                                <input type="text" class="form-control" id="alertTimeStart" placeholder="选择开始时间">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警时间(结束)</label>
                                <input type="text" class="form-control" id="alertTimeEnd" placeholder="选择结束时间">
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="button" class="btn btn-primary me-2" onclick="searchRecords()">
                                    <i class="fas fa-search"></i> 查询
                                </button>
                                <button type="button" class="btn btn-secondary me-2" onclick="resetSearch()">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                                <button type="button" class="btn btn-success" onclick="exportRecords()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 统计信息 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary" id="totalCount">0</h5>
                                <p class="card-text">总记录数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger" id="openCount">0</h5>
                                <p class="card-text">未关闭</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning" id="undisposedCount">0</h5>
                                <p class="card-text">未处置</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success" id="disposedCount">0</h5>
                                <p class="card-text">已处置</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">预警记录列表</h5>
                        <div>
                            <button type="button" class="btn btn-warning btn-sm" onclick="batchDispose()">
                                <i class="fas fa-tasks"></i> 批量处置
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="batchClose()">
                                <i class="fas fa-times-circle"></i> 批量关闭
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        </th>
                                        <th>事项编码</th>
                                        <th>预警对象ID</th>
                                        <th>预警时间</th>
                                        <th>事项描述</th>
                                        <th>预警状态</th>
                                        <th>处置状态</th>
                                        <th>处置人</th>
                                        <th>处置时间</th>
                                        <th width="200">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="recordTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <nav>
                            <ul class="pagination justify-content-center mb-0" id="pagination">
                                <!-- 分页将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 处置模态框 -->
    <div class="modal fade" id="disposeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">预警记录处置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="disposeForm">
                        <input type="hidden" id="disposeRecordId">
                        <div class="mb-3">
                            <label class="form-label">处置结果 <span class="text-danger">*</span></label>
                            <select class="form-select" id="disposeResultCode" required>
                                <option value="">请选择处置结果</option>
                                <option value="1001">已解决</option>
                                <option value="1002">转交处理</option>
                                <option value="1003">暂缓处理</option>
                                <option value="1004">无需处理</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">处置说明 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="disposeReason" rows="4" required 
                                      placeholder="请详细说明处置情况..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitDispose()">确认处置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 关闭模态框 -->
    <div class="modal fade" id="closeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">关闭预警记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="closeForm">
                        <input type="hidden" id="closeRecordId">
                        <div class="mb-3">
                            <label class="form-label">关闭原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="closeReason" rows="4" required 
                                      placeholder="请说明关闭原因..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitClose()">确认关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">预警记录详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detailContent">
                    <!-- 详情内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalRecords = 0;
        let selectedRecords = [];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initDatePickers();
            loadRecords();
        });

        // 初始化日期选择器
        function initDatePickers() {
            flatpickr("#alertTimeStart", {
                locale: "zh",
                dateFormat: "Y-m-d H:i",
                enableTime: true,
                time_24hr: true
            });

            flatpickr("#alertTimeEnd", {
                locale: "zh",
                dateFormat: "Y-m-d H:i",
                enableTime: true,
                time_24hr: true
            });
        }

        // 查询记录
        function searchRecords() {
            currentPage = 1;
            loadRecords();
        }

        // 重置查询条件
        function resetSearch() {
            document.getElementById('searchForm').reset();
            currentPage = 1;
            loadRecords();
        }

        // 加载记录数据
        function loadRecords() {
            const params = {
                page: currentPage,
                size: pageSize,
                itemCode: document.getElementById('itemCode').value,
                alertObjectId: document.getElementById('alertObjectId').value,
                alertStatus: document.getElementById('alertStatus').value,
                disposeStatus: document.getElementById('disposeStatus').value,
                alertTimeStart: document.getElementById('alertTimeStart').value,
                alertTimeEnd: document.getElementById('alertTimeEnd').value
            };

            // 这里应该调用实际的API接口
            // 现在使用模拟数据
            loadMockData(params);
        }

        // 模拟数据加载
        function loadMockData(params) {
            // 模拟API响应数据
            const mockData = {
                total: 156,
                records: [
                    {
                        id: '1',
                        itemCode: 'ALERT001',
                        alertObjectId: 'OBJ001',
                        alertTime: '2024-01-15 10:30:00',
                        itemDesc: '血压异常预警：收缩压180mmHg，舒张压110mmHg，超出正常范围',
                        alertStatus: '0',
                        closeTime: null,
                        closeReason: null,
                        disposeStatus: '0',
                        disposeTime: null,
                        disposeBy: null,
                        disposeResultCode: null,
                        disposeReason: null,
                        createTime: '2024-01-15 10:30:00',
                        createBy: 'system'
                    },
                    {
                        id: '2',
                        itemCode: 'ALERT002',
                        alertObjectId: 'OBJ002',
                        alertTime: '2024-01-15 09:15:00',
                        itemDesc: '血糖异常预警：空腹血糖12.5mmol/L，超出正常范围',
                        alertStatus: '1',
                        closeTime: '2024-01-15 14:20:00',
                        closeReason: '患者已就医，血糖已控制',
                        disposeStatus: '1',
                        disposeTime: '2024-01-15 11:30:00',
                        disposeBy: '张医生',
                        disposeResultCode: '1001',
                        disposeReason: '已联系患者就医，调整用药方案',
                        createTime: '2024-01-15 09:15:00',
                        createBy: 'system'
                    }
                ]
            };

            totalRecords = mockData.total;
            renderTable(mockData.records);
            renderPagination();
            updateStatistics(mockData.records);
        }

        // 渲染表格
        function renderTable(records) {
            const tbody = document.getElementById('recordTableBody');
            tbody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="record-checkbox" value="${record.id}"
                               onchange="updateSelectedRecords()">
                    </td>
                    <td>${record.itemCode}</td>
                    <td>${record.alertObjectId}</td>
                    <td>${record.alertTime}</td>
                    <td title="${record.itemDesc}">
                        ${record.itemDesc.length > 30 ? record.itemDesc.substring(0, 30) + '...' : record.itemDesc}
                    </td>
                    <td>
                        <span class="status-badge ${record.alertStatus === '0' ? 'status-open' : 'status-closed'}">
                            ${record.alertStatus === '0' ? '未关闭' : '已关闭'}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${record.disposeStatus === '0' ? 'status-undisposed' : 'status-disposed'}">
                            ${record.disposeStatus === '0' ? '未处置' : '已处置'}
                        </span>
                    </td>
                    <td>${record.disposeBy || '-'}</td>
                    <td>${record.disposeTime || '-'}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-info" onclick="viewDetail('${record.id}')">
                                <i class="fas fa-eye"></i> 详情
                            </button>
                            ${record.disposeStatus === '0' ?
                                `<button type="button" class="btn btn-outline-warning" onclick="showDisposeModal('${record.id}')">
                                    <i class="fas fa-tasks"></i> 处置
                                </button>` : ''}
                            ${record.alertStatus === '0' ?
                                `<button type="button" class="btn btn-outline-danger" onclick="showCloseModal('${record.id}')">
                                    <i class="fas fa-times"></i> 关闭
                                </button>` : ''}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染分页
        function renderPagination() {
            const totalPages = Math.ceil(totalRecords / pageSize);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) return;

            // 上一页
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>`;
            pagination.appendChild(prevLi);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }

            // 下一页
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>`;
            pagination.appendChild(nextLi);
        }

        // 切换页码
        function changePage(page) {
            if (page < 1 || page > Math.ceil(totalRecords / pageSize)) return;
            currentPage = page;
            loadRecords();
        }

        // 更新统计信息
        function updateStatistics(records) {
            // 这里应该从API获取实际统计数据，现在使用模拟数据
            document.getElementById('totalCount').textContent = totalRecords;
            document.getElementById('openCount').textContent = '45';
            document.getElementById('undisposedCount').textContent = '32';
            document.getElementById('disposedCount').textContent = '111';
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.record-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateSelectedRecords();
        }

        // 更新选中记录
        function updateSelectedRecords() {
            const checkboxes = document.querySelectorAll('.record-checkbox:checked');
            selectedRecords = Array.from(checkboxes).map(cb => cb.value);

            // 更新全选状态
            const selectAll = document.getElementById('selectAll');
            const allCheckboxes = document.querySelectorAll('.record-checkbox');
            selectAll.checked = selectedRecords.length === allCheckboxes.length;
            selectAll.indeterminate = selectedRecords.length > 0 && selectedRecords.length < allCheckboxes.length;
        }

        // 显示处置模态框
        function showDisposeModal(recordId) {
            document.getElementById('disposeRecordId').value = recordId;
            document.getElementById('disposeResultCode').value = '';
            document.getElementById('disposeReason').value = '';

            const modal = new bootstrap.Modal(document.getElementById('disposeModal'));
            modal.show();
        }

        // 提交处置
        function submitDispose() {
            const recordId = document.getElementById('disposeRecordId').value;
            const resultCode = document.getElementById('disposeResultCode').value;
            const reason = document.getElementById('disposeReason').value;

            if (!resultCode || !reason.trim()) {
                alert('请填写完整的处置信息');
                return;
            }

            // 这里应该调用实际的API接口
            console.log('处置记录:', { recordId, resultCode, reason });

            // 模拟API调用成功
            alert('处置成功');
            bootstrap.Modal.getInstance(document.getElementById('disposeModal')).hide();
            loadRecords();
        }

        // 显示关闭模态框
        function showCloseModal(recordId) {
            document.getElementById('closeRecordId').value = recordId;
            document.getElementById('closeReason').value = '';

            const modal = new bootstrap.Modal(document.getElementById('closeModal'));
            modal.show();
        }

        // 提交关闭
        function submitClose() {
            const recordId = document.getElementById('closeRecordId').value;
            const reason = document.getElementById('closeReason').value;

            if (!reason.trim()) {
                alert('请填写关闭原因');
                return;
            }

            // 这里应该调用实际的API接口
            console.log('关闭记录:', { recordId, reason });

            // 模拟API调用成功
            alert('关闭成功');
            bootstrap.Modal.getInstance(document.getElementById('closeModal')).hide();
            loadRecords();
        }

        // 查看详情
        function viewDetail(recordId) {
            // 这里应该调用实际的API接口获取详情
            const mockDetail = {
                id: recordId,
                itemCode: 'ALERT001',
                alertObjectId: 'OBJ001',
                alertTime: '2024-01-15 10:30:00',
                itemDesc: '血压异常预警：收缩压180mmHg，舒张压110mmHg，超出正常范围。建议立即就医检查。',
                alertStatus: '0',
                closeTime: null,
                closeReason: null,
                disposeStatus: '0',
                disposeTime: null,
                disposeBy: null,
                disposeResultCode: null,
                disposeReason: null,
                createTime: '2024-01-15 10:30:00',
                createBy: 'system',
                updateTime: '2024-01-15 10:30:00',
                updateBy: 'system'
            };

            renderDetailModal(mockDetail);
        }

        // 渲染详情模态框
        function renderDetailModal(detail) {
            const content = document.getElementById('detailContent');
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">记录ID:</td>
                                <td>${detail.id}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">事项编码:</td>
                                <td>${detail.itemCode}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">预警对象ID:</td>
                                <td>${detail.alertObjectId}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">预警时间:</td>
                                <td>${detail.alertTime}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">预警状态:</td>
                                <td>
                                    <span class="status-badge ${detail.alertStatus === '0' ? 'status-open' : 'status-closed'}">
                                        ${detail.alertStatus === '0' ? '未关闭' : '已关闭'}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">处置状态:</td>
                                <td>
                                    <span class="status-badge ${detail.disposeStatus === '0' ? 'status-undisposed' : 'status-disposed'}">
                                        ${detail.disposeStatus === '0' ? '未处置' : '已处置'}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">关闭时间:</td>
                                <td>${detail.closeTime || '-'}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">处置时间:</td>
                                <td>${detail.disposeTime || '-'}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">处置人:</td>
                                <td>${detail.disposeBy || '-'}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">处置结果:</td>
                                <td>${getDisposeResultText(detail.disposeResultCode)}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">创建时间:</td>
                                <td>${detail.createTime}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">创建人:</td>
                                <td>${detail.createBy}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="fw-bold">事项描述:</h6>
                        <p class="border p-3 bg-light">${detail.itemDesc}</p>
                    </div>
                    ${detail.closeReason ? `
                    <div class="col-12">
                        <h6 class="fw-bold">关闭原因:</h6>
                        <p class="border p-3 bg-light">${detail.closeReason}</p>
                    </div>
                    ` : ''}
                    ${detail.disposeReason ? `
                    <div class="col-12">
                        <h6 class="fw-bold">处置说明:</h6>
                        <p class="border p-3 bg-light">${detail.disposeReason}</p>
                    </div>
                    ` : ''}
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('detailModal'));
            modal.show();
        }

        // 获取处置结果文本
        function getDisposeResultText(code) {
            const resultMap = {
                '1001': '已解决',
                '1002': '转交处理',
                '1003': '暂缓处理',
                '1004': '无需处理'
            };
            return resultMap[code] || '-';
        }

        // 批量处置
        function batchDispose() {
            if (selectedRecords.length === 0) {
                alert('请选择要处置的记录');
                return;
            }

            if (confirm(`确定要批量处置选中的 ${selectedRecords.length} 条记录吗？`)) {
                // 这里应该调用实际的API接口
                console.log('批量处置记录:', selectedRecords);
                alert('批量处置成功');
                loadRecords();
            }
        }

        // 批量关闭
        function batchClose() {
            if (selectedRecords.length === 0) {
                alert('请选择要关闭的记录');
                return;
            }

            if (confirm(`确定要批量关闭选中的 ${selectedRecords.length} 条记录吗？`)) {
                // 这里应该调用实际的API接口
                console.log('批量关闭记录:', selectedRecords);
                alert('批量关闭成功');
                loadRecords();
            }
        }

        // 导出记录
        function exportRecords() {
            const params = {
                itemCode: document.getElementById('itemCode').value,
                alertObjectId: document.getElementById('alertObjectId').value,
                alertStatus: document.getElementById('alertStatus').value,
                disposeStatus: document.getElementById('disposeStatus').value,
                alertTimeStart: document.getElementById('alertTimeStart').value,
                alertTimeEnd: document.getElementById('alertTimeEnd').value
            };

            // 这里应该调用实际的导出API接口
            console.log('导出参数:', params);
            alert('导出功能开发中...');
        }

        // 阻止分页链接的默认行为
        document.addEventListener('click', function(e) {
            if (e.target.closest('.page-link')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
