package com.chis.zyjk.bigdata.alert.pojo.dto;

import com.chis.zyjk.core.common.pojo.ZyjkDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 预警规则新增DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预警规则新增DTO")
public class AlertRuleCreateDTO extends ZyjkDTO {

    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码", required = true)
    @NotBlank(message = "规则编码不能为空")
    private String ruleCode;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", required = true)
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;

    /**
     * Cron表达式
     */
    @ApiModelProperty(value = "Cron表达式")
    private String cronExpression;

    /**
     * 去重主键表达式 如{org_id}_{person_id}
     */
    @ApiModelProperty(value = "去重主键表达式", example = "{org_id}_{person_id}")
    private String deDupKeyExpression;

    /**
     * 预警值表达式 如{org_id}_{person_id}
     */
    @ApiModelProperty(value = "预警值表达式", example = "{org_id}_{person_id}")
    private String alertValueExpression;

    /**
     * 流程配置表达式
     */
    @ApiModelProperty(value = "流程配置表达式")
    private String liteFlowConfig;

    /**
     * 节点配置json
     */
    @ApiModelProperty(value = "节点配置json")
    private String liteFlowNodeConfig;

    /**
     * 是否启用:1:启用 0:停用
     */
    @ApiModelProperty(value = "是否启用", notes = "1:启用 0:停用", required = true)
    @NotNull(message = "是否启用不能为空")
    private Integer ifEnable;

    /**
     * xxljob任务ID
     */
    @ApiModelProperty(value = "xxljob任务ID")
    private Integer xxljobTaskId;

    /**
     * 执行状态:0:待执行 1:执行中 2:异常
     */
    @ApiModelProperty(value = "执行状态", notes = "0:待执行 1:执行中 2:异常")
    private Integer status;

    /**
     * 异常原因
     */
    @ApiModelProperty(value = "异常原因")
    private String errorMsg;
}
