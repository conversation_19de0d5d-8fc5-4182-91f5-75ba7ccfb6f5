package com.chis.zyjk.bigdata.alert.pojo.dto;

import com.chis.zyjk.core.common.pojo.ZyjkDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 预警规则修改DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预警规则修改DTO")
public class AlertRuleUpdateDTO extends ZyjkDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空")
    private String id;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * Cron表达式
     */
    @ApiModelProperty(value = "Cron表达式")
    private String cronExpression;

    /**
     * 去重主键表达式 如{org_id}_{person_id}
     */
    @ApiModelProperty(value = "去重主键表达式", example = "{org_id}_{person_id}")
    private String deDupKeyExpression;

    /**
     * 预警值表达式 如{org_id}_{person_id}
     */
    @ApiModelProperty(value = "预警值表达式", example = "{org_id}_{person_id}")
    private String alertValueExpression;

    /**
     * 流程配置表达式
     */
    @ApiModelProperty(value = "流程配置表达式")
    private String liteFlowConfig;

    /**
     * 节点配置json
     */
    @ApiModelProperty(value = "节点配置json")
    private String liteFlowNodeConfig;
}
