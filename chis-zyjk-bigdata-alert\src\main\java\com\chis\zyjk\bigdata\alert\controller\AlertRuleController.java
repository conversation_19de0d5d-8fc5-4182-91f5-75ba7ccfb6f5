package com.chis.zyjk.bigdata.alert.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.project.frame.common.tools.core.bean.BeanUtil;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRuleCreateDTO;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRuleQueryDTO;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRuleUpdateDTO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRulePO;
import com.chis.zyjk.bigdata.alert.pojo.vo.AlertRuleVO;
import com.chis.zyjk.bigdata.alert.service.AlertRuleService;
import com.chis.zyjk.core.common.enums.ZyjkResultEnum;
import com.way.common.core.domain.R;
import com.way.common.core.exception.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预警规则管理控制器
 */
@Api(tags = {"预警规则管理"})
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/alertRule")
public class AlertRuleController {

    private final AlertRuleService alertRuleService;

    /**
     * 新增预警规则
     *
     * @param createDTO 新增请求参数
     * @return 操作结果
     */
    @ApiOperation(value = "新增预警规则", notes = "创建新的预警规则配置")
    @PostMapping("/create")
    public R<Void> create(@RequestBody @Validated AlertRuleCreateDTO createDTO) {
        log.info("开始新增预警规则，规则编码: {}", createDTO.getRuleCode());

        // 检查规则编码是否已存在
        AlertRulePO existingRule = alertRuleService.getByRuleCode(createDTO.getRuleCode());
        if (existingRule != null) {
            log.warn("预警规则编码已存在: {}", createDTO.getRuleCode());
            throw new ServiceException("预警规则编码已存在", ZyjkResultEnum.VALIDATED_ERROR.getCode());
        }

        // 转换DTO为PO
        AlertRulePO alertRule = BeanUtil.copyProperties(createDTO, AlertRulePO.class);

        // 保存预警规则
        boolean success = alertRuleService.save(alertRule);
        if (!success) {
            log.error("预警规则保存失败，规则编码: {}", createDTO.getRuleCode());
            throw new ServiceException("预警规则保存失败", ZyjkResultEnum.INTERNAL_SERVER_ERROR.getCode());
        }

        log.info("预警规则新增成功，规则编码: {}", createDTO.getRuleCode());
        return R.ok();
    }

    /**
     * 修改预警规则
     *
     * @param updateDTO 修改请求参数
     * @return 操作结果
     */
    @ApiOperation(value = "修改预警规则", notes = "更新现有的预警规则配置")
    @PostMapping("/update")
    public R<Void> update(@RequestBody @Validated AlertRuleUpdateDTO updateDTO) {
        log.info("开始修改预警规则，ID: {}", updateDTO.getId());

        // 检查规则是否存在
        AlertRulePO existingRule = alertRuleService.getById(updateDTO.getId());
        if (existingRule == null) {
            log.warn("预警规则不存在，ID: {}", updateDTO.getId());
            throw new ServiceException("预警规则不存在", ZyjkResultEnum.VALIDATED_ERROR.getCode());
        }

        // 转换DTO为PO，只更新非空字段
        AlertRulePO alertRule = BeanUtil.copyProperties(updateDTO, AlertRulePO.class);

        // 更新预警规则
        boolean success = alertRuleService.updateById(alertRule);
        if (!success) {
            log.error("预警规则更新失败，ID: {}", updateDTO.getId());
            throw new ServiceException("预警规则更新失败", ZyjkResultEnum.INTERNAL_SERVER_ERROR.getCode());
        }

        log.info("预警规则修改成功，ID: {}", updateDTO.getId());
        return R.ok();
    }

    /**
     * 查询单个预警规则
     *
     * @param queryDTO 查询请求参数
     * @return 预警规则详情
     */
    @ApiOperation(value = "查询单个预警规则",
            notes = "根据ID或规则编码查询预警规则详情，优先使用ID查询")
    @PostMapping("/get")
    public R<AlertRuleVO> get(@RequestBody @Validated AlertRuleQueryDTO queryDTO) {
        log.info("开始查询预警规则，ID: {}, 规则编码: {}", queryDTO.getId(), queryDTO.getRuleCode());

        AlertRulePO alertRule = null;

        // 优先使用ID查询
        if (StrUtil.isNotBlank(queryDTO.getId())) {
            alertRule = alertRuleService.getById(queryDTO.getId());
        } else if (StrUtil.isNotBlank(queryDTO.getRuleCode())) {
            alertRule = alertRuleService.getByRuleCode(queryDTO.getRuleCode());
        } else {
            log.warn("查询参数不完整，ID和规则编码都为空");
            throw new ServiceException("查询参数不完整，ID和规则编码都为空", ZyjkResultEnum.VALIDATED_ERROR.getCode());
        }

        if (alertRule == null) {
            log.warn("预警规则不存在，ID: {}, 规则编码: {}", queryDTO.getId(), queryDTO.getRuleCode());
            throw new ServiceException("预警规则不存在", ZyjkResultEnum.INVOKE_SERVER_ERROR.getCode());
        }

        // 转换PO为VO
        AlertRuleVO result = BeanUtil.copyProperties(alertRule, AlertRuleVO.class);

        log.info("预警规则查询成功，规则编码: {}", alertRule.getRuleCode());
        return R.ok(result);
    }

    /**
     * 分页查询预警规则
     *
     * @param queryDTO 查询请求参数
     * @return 分页结果
     */
    @ApiOperation(value = "分页查询预警规则",
            notes = "根据条件分页查询预警规则列表，支持按规则编码、规则名称、状态等条件筛选")
    @PostMapping("/page")
    public R<Page<AlertRuleVO>> page(@RequestBody @Validated AlertRuleQueryDTO queryDTO) {
        log.info("开始分页查询预警规则，页码: {}, 每页大小: {}", queryDTO.getCurrent(), queryDTO.getSize());

        // 设置默认分页参数
        Long current = queryDTO.getCurrent() != null ? queryDTO.getCurrent() : 1L;
        Long size = queryDTO.getSize() != null ? queryDTO.getSize() : 10L;

        Page<AlertRulePO> page = new Page<>(current, size);

        // 执行分页查询
        Page<AlertRulePO> resultPage = alertRuleService.page(page,
                queryDTO.getRuleCode(),
                queryDTO.getRuleName(),
                queryDTO.getStatus());

        // 转换PO为VO
        List<AlertRuleVO> voList = resultPage.getRecords().stream()
                .map(po -> BeanUtil.copyProperties(po, AlertRuleVO.class))
                .collect(Collectors.toList());

        Page<AlertRuleVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        voPage.setRecords(voList);

        log.info("预警规则分页查询完成，返回数据量: {}", voList.size());
        return R.ok(voPage);
    }
}
