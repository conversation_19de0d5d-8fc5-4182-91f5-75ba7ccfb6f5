package com.chis.zyjk.bigdata.alert.pojo.dto;

import com.chis.zyjk.core.common.pojo.ZyjkDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警规则查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预警规则查询DTO")
public class AlertRuleQueryDTO extends ZyjkDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    private String ruleCode;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 是否启用:1:启用 0:停用
     */
    @ApiModelProperty(value = "是否启用", notes = "1:启用 0:停用")
    private Integer ifEnable;

    /**
     * 执行状态:0:待执行 1:执行中 2:异常
     */
    @ApiModelProperty(value = "执行状态", notes = "0:待执行 1:执行中 2:异常")
    private Integer status;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", example = "1")
    private Long current;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "10")
    private Long size;
}
