package com.chis.zyjk.bigdata.alert.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 预警规则响应VO
 */
@Data
@ApiModel(description = "预警规则响应VO")
public class AlertRuleVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    private String ruleCode;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * Cron表达式
     */
    @ApiModelProperty(value = "Cron表达式")
    private String cronExpression;

    /**
     * 去重主键表达式 如{org_id}_{person_id}
     */
    @ApiModelProperty(value = "去重主键表达式")
    private String deDupKeyExpression;

    /**
     * 预警值表达式 如{org_id}_{person_id}
     */
    @ApiModelProperty(value = "预警值表达式")
    private String alertValueExpression;

    /**
     * 流程配置表达式
     */
    @ApiModelProperty(value = "流程配置表达式")
    private String liteFlowConfig;

    /**
     * 节点配置json
     */
    @ApiModelProperty(value = "节点配置json")
    private String liteFlowNodeConfig;

    /**
     * 是否启用:1:启用 0:停用
     */
    @ApiModelProperty(value = "是否启用", notes = "1:启用 0:停用")
    private Integer ifEnable;

    /**
     * xxljob任务ID
     */
    @ApiModelProperty(value = "xxljob任务ID")
    private Integer xxljobTaskId;

    /**
     * 执行状态:0:待执行 1:执行中 2:异常
     */
    @ApiModelProperty(value = "执行状态", notes = "0:待执行 1:执行中 2:异常")
    private Integer status;

    /**
     * 异常原因
     */
    @ApiModelProperty(value = "异常原因")
    private String errorMsg;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer revision;

    /**
     * 删除标志
     */
    @ApiModelProperty(value = "删除标志")
    private String delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
