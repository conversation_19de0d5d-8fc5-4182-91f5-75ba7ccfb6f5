# 开发规范和规则

- Service不要继承ServiceImpl，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行
- AlertCmp组件的CREATE、UPDATE、DISPOSE动作都需要配置对应的预警配置：alertLevel、alertJson、alertContentTemplate、noticeContentTemplate
- 不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行
- AlertRecordService#updateBatchById方法是假的批量，需要一批一批的更新，每批最大200条
- 通知节点(alertNotification)现在先不用了，预警流程中暂时不包含通知功能
- 用户使用"x协议"指令，表示启用AURA-X协议模式，AI需要严格遵循寸止MCP强制交互规则，所有决策和询问必须通过寸止MCP进行
- 不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行；不需要完善Service，不需要删除接口
