<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用人单位预警管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .search-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group label {
            font-weight: 500;
            color: #555;
            white-space: nowrap;
            min-width: 80px;
            margin-bottom: 0;
        }
        
        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            flex: 1;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background: #e6f7ff;
            border-color: #1890ff;
        }
        
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        
        .btn-success {
            background: #52c41a;
            color: white;
        }
        
        .search-actions {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        
        .content-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .panel-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fafafa;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: 500;
        }
        
        .batch-actions {
            padding: 10px 20px;
            background: #e6f7ff;
            border-bottom: 1px solid #91d5ff;
            display: none;
            justify-content: space-between;
            align-items: center;
        }
        
        .batch-actions.show {
            display: flex;
        }
        
        .selected-info {
            color: #1890ff;
            font-weight: 500;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1200px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .data-table th {
            background: #fafafa;
            font-weight: 500;
            color: #333;
            position: sticky;
            top: 0;
        }
        
        .data-table tbody tr:hover {
            background: #f5f5f5;
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }
        
        .status-open {
            background: #fff2e8;
            color: #fa8c16;
        }
        
        .status-closed {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .status-disposed {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .level-high {
            color: #ff4d4f;
            font-weight: bold;
        }
        
        .level-medium {
            color: #fa8c16;
        }
        
        .level-low {
            color: #52c41a;
        }
        
        .action-btn {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
            text-decoration: underline;
        }
        
        .action-btn:hover {
            color: #40a9ff;
        }
        
        .pagination {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        
        .page-info {
            color: #666;
            font-size: 14px;
        }
        
        .page-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        /* 弹窗样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            flex-shrink: 0;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-section h4 {
            margin-bottom: 10px;
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
        }
        
        .info-item label {
            font-weight: 500;
            color: #666;
            margin-right: 8px;
            min-width: 80px;
        }
        
        .required {
            color: #ff4d4f;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 80px;
        }

        .checkbox-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            flex: 1;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            font-weight: normal;
            cursor: pointer;
            margin-bottom: 0;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 6px;
            cursor: pointer;
        }
        
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            z-index: 2000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .message.success {
            background: #52c41a;
        }
        
        .message.error {
            background: #ff4d4f;
        }
        
        .message.warning {
            background: #fa8c16;
        }
        
        .message.info {
            background: #1890ff;
        }
        
        @media (max-width: 1024px) {
            .search-form {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }

            .panel-header {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>用人单位预警管理</h1>
        </div>
        
        <!-- 搜索面板 -->
        <div class="search-panel">
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label>行政区划</label>
                    <select class="form-control" name="adminDivision">
                        <option value="">全部区划</option>
                        <option value="110000">北京市</option>
                        <option value="110100">北京市市辖区</option>
                        <option value="110101">东城区</option>
                        <option value="110102">西城区</option>
                        <option value="110105">朝阳区</option>
                        <option value="110106">丰台区</option>
                        <option value="110107">石景山区</option>
                        <option value="110108">海淀区</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>单位名称</label>
                    <input type="text" class="form-control" name="unitName" placeholder="请输入单位名称">
                </div>

                <div class="form-group">
                    <label>预警事项</label>
                    <select class="form-control" name="itemCode">
                        <option value="">全部事项</option>
                        <option value="PATIENT_OVERDUE">患者超期预警</option>
                        <option value="ORDER_ABNORMAL">医嘱异常预警</option>
                        <option value="LAB_CRITICAL">检验危急值</option>
                        <option value="VITAL_ABNORMAL">生命体征异常</option>
                        <option value="DRUG_INTERACTION">药物相互作用</option>
                        <option value="INFECTION_CONTROL">感染控制预警</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>预警级别</label>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="alertLevel" value="HIGH"> 高风险
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="alertLevel" value="MEDIUM"> 中风险
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="alertLevel" value="LOW"> 低风险
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label>预警时间</label>
                    <input type="date" class="form-control" name="alertTime">
                </div>

                <div class="form-group">
                    <label>预警状态</label>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="alertStatus" value="0"> 未关闭
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="alertStatus" value="1"> 已关闭
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label>处置时间</label>
                    <input type="date" class="form-control" name="disposeTime">
                </div>

                <div class="form-group">
                    <label>处置状态</label>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="disposeStatus" value="0"> 未处置
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="disposeStatus" value="1"> 已处置
                        </label>
                    </div>
                </div>

            </form>

            <!-- 查询按钮单独一行 -->
            <div class="search-actions" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 15px;">
                <button type="submit" form="searchForm" class="btn btn-primary">查询</button>
                <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                <button type="button" class="btn btn-secondary" onclick="exportData()">导出</button>
            </div>
        </div>

        <!-- 内容面板 -->
        <div class="content-panel">
            <div class="panel-header">
                <div class="panel-title">
                    用人单位预警列表
                    <span style="color: #999; font-weight: normal;">
                        (共 <span id="totalCount">0</span> 条)
                    </span>
                </div>
                <div>
                    <button class="btn btn-secondary" onclick="refreshData()">刷新</button>
                </div>
            </div>

            <!-- 批量操作栏 -->
            <div class="batch-actions" id="batchActions">
                <div class="selected-info">
                    已选择 <span id="selectedCount">0</span> 条记录
                </div>
                <div>
                    <button class="btn btn-primary" onclick="batchDispose()">批量处置</button>
                    <button class="btn btn-secondary" onclick="clearSelection()">取消选择</button>
                </div>
            </div>


            <!-- 数据表格 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th width="120">行政区划</th>
                            <th width="150">单位名称</th>
                            <th width="80">预警级别</th>
                            <th width="150">预警事项</th>
                            <th width="200">事项描述</th>
                            <th width="120">预警时间</th>
                            <th width="80">预警状态</th>
                            <th width="80">处置状态</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody id="alertTableBody">
                        <!-- 示例数据 -->
                        <tr>
                            <td><input type="checkbox" class="row-checkbox" value="1"></td>
                            <td>朝阳区</td>
                            <td>北京朝阳医院</td>
                            <td><span class="level-high">高风险</span></td>
                            <td>患者超期预警</td>
                            <td title="患者张三住院已超过7天，请及时关注病情进展和治疗效果">患者张三住院已超过7天，请及时关注病情进展...</td>
                            <td>2024-01-15 10:30</td>
                            <td><span class="status-badge status-open">未关闭</span></td>
                            <td><span class="status-badge status-open">未处置</span></td>
                            <td>
                                <button class="action-btn" onclick="showDisposeModal('1')">处置</button>
                                <button class="action-btn" onclick="viewDetail('1')">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="row-checkbox" value="2"></td>
                            <td>海淀区</td>
                            <td>北京大学第三医院</td>
                            <td><span class="level-medium">中风险</span></td>
                            <td>检验危急值</td>
                            <td title="患者王五血钾浓度6.8mmol/L，超出正常范围，需要立即处理">患者王五血钾浓度6.8mmol/L，超出正常范围...</td>
                            <td>2024-01-15 09:15</td>
                            <td><span class="status-badge status-closed">已关闭</span></td>
                            <td><span class="status-badge status-disposed">已处置</span></td>
                            <td>
                                <button class="action-btn" onclick="viewDetail('2')">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="row-checkbox" value="3"></td>
                            <td>西城区</td>
                            <td>北京儿童医院</td>
                            <td><span class="level-low">低风险</span></td>
                            <td>药物相互作用</td>
                            <td title="患儿李小明同时使用阿司匹林和华法林，存在出血风险">患儿李小明同时使用阿司匹林和华法林...</td>
                            <td>2024-01-15 14:20</td>
                            <td><span class="status-badge status-open">未关闭</span></td>
                            <td><span class="status-badge status-disposed">已处置</span></td>
                            <td>
                                <button class="action-btn" onclick="viewDetail('3')">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="row-checkbox" value="4"></td>
                            <td>丰台区</td>
                            <td>北京天坛医院</td>
                            <td><span class="level-medium">中风险</span></td>
                            <td>感染控制预警</td>
                            <td title="病房内发现多例同类型感染病例，需要加强感染控制措施">病房内发现多例同类型感染病例，需要加强感染控制...</td>
                            <td>2024-01-14 16:45</td>
                            <td><span class="status-badge status-closed">已关闭</span></td>
                            <td><span class="status-badge status-open">未处置</span></td>
                            <td>
                                <button class="action-btn" onclick="showDisposeModal('4')">处置</button>
                                <button class="action-btn" onclick="viewDetail('4')">详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="page-info">
                    显示 1 - 4 条，共 4 条记录
                </div>
                <div class="page-controls">
                    <select onchange="changePageSize(this.value)">
                        <option value="20">20条/页</option>
                        <option value="50">50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                    <button class="btn btn-secondary" onclick="prevPage()">上一页</button>
                    <span>第 1 页，共 1 页</span>
                    <button class="btn btn-secondary" onclick="nextPage()">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 处置弹窗 -->
    <div id="disposeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">用人单位预警处置</div>
                <button class="modal-close" onclick="closeModal('disposeModal')">×</button>
            </div>

            <div class="modal-body">
                <!-- 预警信息 -->
                <div class="info-section">
                    <h4>预警信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>行政区划：</label>
                            <span id="modalAdminDivision">朝阳区</span>
                        </div>
                        <div class="info-item">
                            <label>单位名称：</label>
                            <span id="modalUnitName">北京朝阳医院</span>
                        </div>
                        <div class="info-item">
                            <label>预警级别：</label>
                            <span id="modalAlertLevel" class="level-high">高风险</span>
                        </div>
                        <div class="info-item">
                            <label>预警事项：</label>
                            <span id="modalItemName">患者超期预警</span>
                        </div>
                        <div class="info-item">
                            <label>预警时间：</label>
                            <span id="modalAlertTime">2024-01-15 10:30:00</span>
                        </div>
                        <div class="info-item">
                            <label>预警状态：</label>
                            <span id="modalAlertStatus" class="status-badge status-open">未关闭</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <label style="font-weight: 500; color: #666;">事项描述：</label>
                        <div id="modalItemDesc" style="margin-top: 5px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
                            患者张三住院已超过7天，请及时关注病情进展和治疗效果，建议医生及时评估患者病情，制定后续治疗方案。
                        </div>
                    </div>
                </div>

                <!-- 关闭信息（如果有） -->
                <div class="info-section" id="disposeModalCloseInfo" style="display: none;">
                    <h4>关闭信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>关闭时间：</label>
                            <span id="disposeModalCloseTime">2024-01-15 16:00:00</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <label style="font-weight: 500; color: #666;">关闭原因：</label>
                        <div id="disposeModalCloseReason" style="margin-top: 5px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
                            处置完成后自动关闭，患者已按新的治疗方案执行，病情稳定。
                        </div>
                    </div>
                </div>

                <!-- 处置表单 -->
                <div class="info-section">
                    <h4>处置信息</h4>
                    <form id="disposeForm">
                        <div class="form-group">
                            <label>处置结果 <span class="required">*</span></label>
                            <select class="form-control" name="disposeResultCode" required>
                                <option value="">请选择处置结果</option>
                                <option value="RESOLVED">问题已解决</option>
                                <option value="TRANSFERRED">已转科处理</option>
                                <option value="FOLLOW_UP">需要跟进</option>
                                <option value="FALSE_POSITIVE">误报</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>处置说明 <span class="required">*</span></label>
                            <textarea class="form-control" name="disposeReason" rows="4"
                                    placeholder="请详细说明处置过程和结果..." required></textarea>
                        </div>
                    </form>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('disposeModal')">取消</button>
                <button class="btn btn-primary" onclick="submitDispose()">确认处置</button>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">用人单位预警详情</div>
                <button class="modal-close" onclick="closeModal('detailModal')">×</button>
            </div>

            <div class="modal-body">
                <!-- 预警信息 -->
                <div class="info-section">
                    <h4>预警信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>行政区划：</label>
                            <span id="detailAdminDivision">朝阳区</span>
                        </div>
                        <div class="info-item">
                            <label>单位名称：</label>
                            <span id="detailUnitName">北京朝阳医院</span>
                        </div>
                        <div class="info-item">
                            <label>预警级别：</label>
                            <span id="detailAlertLevel" class="level-high">高风险</span>
                        </div>
                        <div class="info-item">
                            <label>预警事项：</label>
                            <span id="detailItemName">患者超期预警</span>
                        </div>
                        <div class="info-item">
                            <label>预警时间：</label>
                            <span id="detailAlertTime">2024-01-15 10:30:00</span>
                        </div>
                        <div class="info-item">
                            <label>预警状态：</label>
                            <span id="detailAlertStatus" class="status-badge status-open">未关闭</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <label style="font-weight: 500; color: #666;">事项描述：</label>
                        <div id="detailItemDesc" style="margin-top: 5px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
                            患者张三住院已超过7天，请及时关注病情进展和治疗效果，建议医生及时评估患者病情，制定后续治疗方案。
                        </div>
                    </div>
                </div>

                <!-- 处置信息 -->
                <div class="info-section" id="disposeInfoSection">
                    <h4>处置信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>处置时间：</label>
                            <span id="detailDisposeTime">2024-01-15 14:30:00</span>
                        </div>
                        <div class="info-item">
                            <label>处置机构：</label>
                            <span id="detailDisposeOrg">北京朝阳医院</span>
                        </div>
                        <div class="info-item">
                            <label>处置人：</label>
                            <span id="detailDisposeBy">李医生</span>
                        </div>
                        <div class="info-item">
                            <label>处置结果：</label>
                            <span id="detailDisposeResult">问题已解决</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <label style="font-weight: 500; color: #666;">处置说明：</label>
                        <div id="detailDisposeReason" style="margin-top: 5px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
                            已联系主治医生，重新评估患者病情，调整治疗方案，患者病情稳定，预计3天内可出院。
                        </div>
                    </div>
                </div>

                <!-- 关闭信息 -->
                <div class="info-section" id="closeInfoSection" style="display: none;">
                    <h4>关闭信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>关闭时间：</label>
                            <span id="detailCloseTime">2024-01-15 16:00:00</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <label style="font-weight: 500; color: #666;">关闭原因：</label>
                        <div id="detailCloseReason" style="margin-top: 5px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
                            处置完成后自动关闭，患者已按新的治疗方案执行，病情稳定。
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('detailModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 批量处置模态框 -->
    <div id="batchDisposeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">批量处置预警</div>
                <button class="modal-close" onclick="closeModal('batchDisposeModal')">×</button>
            </div>

            <div class="modal-body">
                <!-- 批量处置提示 -->
                <div style="margin-bottom: 20px; padding: 12px; background: #e6f7ff; border-radius: 4px; border-left: 4px solid #1890ff;">
                    <span style="color: #1890ff; font-weight: 500;">
                        将对已选择的 <span id="batchSelectedCount">0</span> 条预警记录进行批量处置
                    </span>
                </div>

                <!-- 批量处置表单 -->
                <div class="info-section">
                    <h4>批量处置信息</h4>
                    <form id="batchDisposeForm">
                        <div class="form-group">
                            <label>处置结果 <span class="required">*</span></label>
                            <select class="form-control" name="disposeResultCode" required>
                                <option value="">请选择处置结果</option>
                                <option value="RESOLVED">问题已解决</option>
                                <option value="TRANSFERRED">已转科处理</option>
                                <option value="FOLLOW_UP">需要跟进</option>
                                <option value="FALSE_POSITIVE">误报</option>
                                <option value="BATCH_PROCESSED">批量处理</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>处置说明 <span class="required">*</span></label>
                            <textarea class="form-control" name="disposeReason" rows="4"
                                    placeholder="请详细说明批量处置的原因和处理方式..." required></textarea>
                        </div>
                    </form>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('batchDisposeModal')">取消</button>
                <button class="btn btn-primary" onclick="submitBatchDispose()">确认批量处置</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedRows = new Set();
        let currentPage = 1;
        let pageSize = 20;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
            updateTotalCount();
        });

        // 初始化事件监听
        function initEventListeners() {
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                searchAlerts();
            });

            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('row-checkbox')) {
                    updateSelection();
                }
            });
        }

        // 搜索预警
        function searchAlerts() {
            // 获取复选框选中的值
            const alertLevelValues = Array.from(document.querySelectorAll('input[name="alertLevel"]:checked')).map(cb => cb.value);
            const alertStatusValues = Array.from(document.querySelectorAll('input[name="alertStatus"]:checked')).map(cb => cb.value);
            const disposeStatusValues = Array.from(document.querySelectorAll('input[name="disposeStatus"]:checked')).map(cb => cb.value);

            console.log('预警级别:', alertLevelValues);
            console.log('预警状态:', alertStatusValues);
            console.log('处置状态:', disposeStatusValues);

            showMessage('搜索功能需要连接后端API', 'info');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchForm').reset();
            // 重置复选框
            document.querySelectorAll('input[name="alertLevel"]').forEach(cb => cb.checked = false);
            document.querySelectorAll('input[name="alertStatus"]').forEach(cb => cb.checked = false);
            document.querySelectorAll('input[name="disposeStatus"]').forEach(cb => cb.checked = false);
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.row-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedRows.add(checkbox.value);
                } else {
                    selectedRows.delete(checkbox.value);
                }
            });

            updateSelection();
        }

        // 更新选择状态
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.row-checkbox');
            selectedRows.clear();

            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedRows.add(checkbox.value);
                }
            });

            const selectedCount = selectedRows.size;
            document.getElementById('selectedCount').textContent = selectedCount;

            const batchActions = document.getElementById('batchActions');
            if (selectedCount > 0) {
                batchActions.classList.add('show');
            } else {
                batchActions.classList.remove('show');
            }

            const selectAll = document.getElementById('selectAll');
            selectAll.checked = selectedCount === checkboxes.length && checkboxes.length > 0;
        }

        // 清除选择
        function clearSelection() {
            selectedRows.clear();
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('selectAll').checked = false;
            document.getElementById('batchActions').classList.remove('show');
        }

        // 显示处置弹窗
        function showDisposeModal(alertId) {
            // 根据alertId获取预警信息（这里使用模拟数据）
            const mockAlertData = {
                '1': {
                    adminDivision: '朝阳区',
                    unitName: '北京朝阳医院',
                    alertLevel: '高风险',
                    itemName: '患者超期预警',
                    alertTime: '2024-01-15 10:30:00',
                    alertStatus: '未关闭',
                    itemDesc: '患者张三住院已超过7天，请及时关注病情进展和治疗效果，建议医生及时评估患者病情，制定后续治疗方案。',
                    closeTime: null,
                    closeReason: null
                },
                '3': {
                    adminDivision: '西城区',
                    unitName: '北京儿童医院',
                    alertLevel: '低风险',
                    itemName: '药物相互作用',
                    alertTime: '2024-01-15 14:20:00',
                    alertStatus: '未关闭',
                    itemDesc: '患儿李小明同时使用阿司匹林和华法林，存在出血风险',
                    closeTime: null,
                    closeReason: null
                },
                '4': {
                    adminDivision: '丰台区',
                    unitName: '北京天坛医院',
                    alertLevel: '中风险',
                    itemName: '感染控制预警',
                    alertTime: '2024-01-14 16:45:00',
                    alertStatus: '已关闭',
                    itemDesc: '病房内发现多例同类型感染病例，需要加强感染控制措施',
                    closeTime: '2024-01-15 08:00:00',
                    closeReason: '经调查为误报，实际为不同病原体感染，无需特殊处置。'
                }
            };

            const data = mockAlertData[alertId] || mockAlertData['1'];

            // 填充预警信息
            document.getElementById('modalAdminDivision').textContent = data.adminDivision;
            document.getElementById('modalUnitName').textContent = data.unitName;
            document.getElementById('modalAlertLevel').textContent = data.alertLevel;
            document.getElementById('modalAlertLevel').className = `level-${data.alertLevel === '高风险' ? 'high' : data.alertLevel === '中风险' ? 'medium' : 'low'}`;
            document.getElementById('modalItemName').textContent = data.itemName;
            document.getElementById('modalAlertTime').textContent = data.alertTime;
            document.getElementById('modalAlertStatus').textContent = data.alertStatus;
            document.getElementById('modalAlertStatus').className = `status-badge ${data.alertStatus === '未关闭' ? 'status-open' : 'status-closed'}`;
            document.getElementById('modalItemDesc').textContent = data.itemDesc;

            // 关闭信息（如果有）
            const closeInfoSection = document.getElementById('disposeModalCloseInfo');
            if (data.closeTime) {
                closeInfoSection.style.display = 'block';
                document.getElementById('disposeModalCloseTime').textContent = data.closeTime;
                document.getElementById('disposeModalCloseReason').textContent = data.closeReason;
            } else {
                closeInfoSection.style.display = 'none';
            }

            document.getElementById('disposeForm').dataset.alertId = alertId;
            document.getElementById('disposeModal').classList.add('show');
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 提交处置
        function submitDispose() {
            const form = document.getElementById('disposeForm');

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            showMessage('处置成功', 'success');
            closeModal('disposeModal');
        }

        // 批量处置
        function batchDispose() {
            if (selectedRows.size === 0) {
                showMessage('请先选择要处置的记录', 'warning');
                return;
            }

            // 更新选中记录数量
            document.getElementById('batchSelectedCount').textContent = selectedRows.size;

            // 显示批量处置模态框
            document.getElementById('batchDisposeModal').classList.add('show');
        }

        // 提交批量处置
        function submitBatchDispose() {
            const form = document.getElementById('batchDisposeForm');

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            const disposeData = Object.fromEntries(formData.entries());

            console.log('批量处置数据:', {
                alertIds: Array.from(selectedRows),
                disposeInfo: disposeData
            });

            showMessage(`成功批量处置 ${selectedRows.size} 条记录`, 'success');
            closeModal('batchDisposeModal');
            clearSelection();
        }



        // 查看详情
        function viewDetail(alertId) {
            // 根据alertId获取详情数据（这里使用模拟数据）
            const mockDetailData = {
                '1': {
                    adminDivision: '朝阳区',
                    unitName: '北京朝阳医院',
                    alertLevel: '高风险',
                    itemName: '患者超期预警',
                    alertTime: '2024-01-15 10:30:00',
                    alertStatus: '未关闭',
                    itemDesc: '患者张三住院已超过7天，请及时关注病情进展和治疗效果，建议医生及时评估患者病情，制定后续治疗方案。',
                    disposeTime: null,
                    disposeOrg: null,
                    disposeBy: null,
                    disposeResult: null,
                    disposeReason: null,
                    closeTime: null,
                    closeReason: null
                },
                '2': {
                    adminDivision: '海淀区',
                    unitName: '北京大学第三医院',
                    alertLevel: '中风险',
                    itemName: '检验危急值',
                    alertTime: '2024-01-15 09:15:00',
                    alertStatus: '已关闭',
                    itemDesc: '患者王五血钾浓度6.8mmol/L，超出正常范围，需要立即处理',
                    disposeTime: '2024-01-15 11:30:00',
                    disposeOrg: '北京大学第三医院',
                    disposeBy: '赵医生',
                    disposeResult: '问题已解决',
                    disposeReason: '已立即给予降钾治疗，复查血钾恢复正常范围，患者生命体征稳定。',
                    closeTime: '2024-01-15 14:20:00',
                    closeReason: '处置完成后自动关闭，患者血钾已恢复正常。'
                },
                '3': {
                    adminDivision: '西城区',
                    unitName: '北京儿童医院',
                    alertLevel: '低风险',
                    itemName: '药物相互作用',
                    alertTime: '2024-01-15 14:20:00',
                    alertStatus: '未关闭',
                    itemDesc: '患儿李小明同时使用阿司匹林和华法林，存在出血风险',
                    disposeTime: '2024-01-15 15:30:00',
                    disposeOrg: '北京儿童医院',
                    disposeBy: '王医生',
                    disposeResult: '已调整用药',
                    disposeReason: '已停用阿司匹林，调整抗凝方案，密切监测凝血功能。',
                    closeTime: null,
                    closeReason: null
                },
                '4': {
                    adminDivision: '丰台区',
                    unitName: '北京天坛医院',
                    alertLevel: '中风险',
                    itemName: '感染控制预警',
                    alertTime: '2024-01-14 16:45:00',
                    alertStatus: '已关闭',
                    itemDesc: '病房内发现多例同类型感染病例，需要加强感染控制措施',
                    disposeTime: null,
                    disposeOrg: null,
                    disposeBy: null,
                    disposeResult: null,
                    disposeReason: null,
                    closeTime: '2024-01-15 08:00:00',
                    closeReason: '经调查为误报，实际为不同病原体感染，无需特殊处置。'
                }
            };

            const data = mockDetailData[alertId] || mockDetailData['1'];

            // 填充预警信息
            document.getElementById('detailAdminDivision').textContent = data.adminDivision;
            document.getElementById('detailUnitName').textContent = data.unitName;
            document.getElementById('detailAlertLevel').textContent = data.alertLevel;
            document.getElementById('detailAlertLevel').className = `level-${data.alertLevel === '高风险' ? 'high' : data.alertLevel === '中风险' ? 'medium' : 'low'}`;
            document.getElementById('detailItemName').textContent = data.itemName;
            document.getElementById('detailAlertTime').textContent = data.alertTime;
            document.getElementById('detailAlertStatus').textContent = data.alertStatus;
            document.getElementById('detailAlertStatus').className = `status-badge ${data.alertStatus === '未关闭' ? 'status-open' : 'status-closed'}`;
            document.getElementById('detailItemDesc').textContent = data.itemDesc;

            // 处置信息
            const disposeSection = document.getElementById('disposeInfoSection');
            if (data.disposeTime) {
                disposeSection.style.display = 'block';
                document.getElementById('detailDisposeTime').textContent = data.disposeTime;
                document.getElementById('detailDisposeOrg').textContent = data.disposeOrg;
                document.getElementById('detailDisposeBy').textContent = data.disposeBy;
                document.getElementById('detailDisposeResult').textContent = data.disposeResult;
                document.getElementById('detailDisposeReason').textContent = data.disposeReason;
            } else {
                disposeSection.style.display = 'none';
            }

            // 关闭信息
            const closeSection = document.getElementById('closeInfoSection');
            if (data.closeTime) {
                closeSection.style.display = 'block';
                document.getElementById('detailCloseTime').textContent = data.closeTime;
                document.getElementById('detailCloseReason').textContent = data.closeReason;
            } else {
                closeSection.style.display = 'none';
            }

            // 显示模态框
            document.getElementById('detailModal').classList.add('show');
        }

        // 导出数据
        function exportData() {
            showMessage('导出功能开发中', 'info');
        }

        // 刷新数据
        function refreshData() {
            showMessage('数据已刷新', 'success');
        }

        // 分页相关
        function changePageSize(size) {
            pageSize = parseInt(size);
        }

        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
            }
        }

        function nextPage() {
            currentPage++;
        }

        // 更新总数显示
        function updateTotalCount() {
            document.getElementById('totalCount').textContent = '4';
        }

        // 消息提示
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                if (document.body.contains(messageDiv)) {
                    document.body.removeChild(messageDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>
