<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.zyjk.bigdata.alert.mapper.AlertRuleMapper">

    <resultMap id="BaseResultMap" type="com.chis.zyjk.bigdata.alert.pojo.po.AlertRulePO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="cronExpression" column="cron_expression" jdbcType="VARCHAR"/>
        <result property="deDupKeyExpression" column="de_dup_key_expression" jdbcType="VARCHAR"/>
        <result property="alertValueExpression" column="alert_value_expression" jdbcType="VARCHAR"/>
        <result property="liteFlowConfig" column="lite_flow_config" jdbcType="LONGVARCHAR"/>
        <result property="liteFlowNodeConfig" column="lite_flow_node_config" jdbcType="LONGVARCHAR"/>
        <result property="ifEnable" column="if_enable" jdbcType="TINYINT"/>
        <result property="xxljobTaskId" column="xxljob_task_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="errorMsg" column="error_msg" jdbcType="INTEGER"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_code, rule_name, cron_expression, dedup_key_expression, alert_value_expression, lite_flow_config,
        lite_flow_node_config, if_enable, xxljob_task_id, status, error_msg,
        revision, del_flag, create_time, create_by, update_time, update_by
    </sql>

</mapper>
